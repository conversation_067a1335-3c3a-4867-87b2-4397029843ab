
河南农投农业大数据科技有限公司
担保业务系统项目
建设功能需求规格说明书












2025年08月

目  录
项目建设功能需求规格说明书	1
一、 项目概况	3
二、 功能需求	3
（一） 担保综合管理平台	3
1． 工作台	4
2． 客户管理	4
3． 反担保物管理	12
4． 业务办理	15
5． 费用管理	19
6． 保后管理	20
7． 综合管理	23
8． 档案管理	24
9． 财务管理	24
10． 统计报表	25
11． 系统设置	26
（二） 移动端管理	30
（三） 历史数据迁移	31
三、 系统集成需求	32
（一） 集团内部系统集成	32
（二） 外部系统集成	32
四、 技术开发需求	33
（一） 平台技术要求	33
（二） 性能要求	34
（三） 系统安全与稳定性需求	34
（四） 系统扩展性需求	35
五、 系统部署需求	35
六、 配套硬件	36
七、 测试要求	37
八、 项目培训要求	37
九、 数据备份要求	38
十、 灾难恢复计划	38
十一、 技术支持要求	38
（一） 维保服务要求	38
（二） 系统升级服务要求	39










项目概况
引言
随着河南农投信用服务有限公司（以下简称“农投信用”）业务规模的持续扩大、担保产品类型的日益丰富、风险管理要求的不断提高以及监管合规政策的日趋严格，现有依赖手工操作、分散式Excel表格管理或功能有限的旧有信息系统已难以满足农投信用精细化、规范化、高效化运营的需求，面临着业务处理效率低下、风险管理精细化不足、数据孤岛与决策支持薄弱、内控合规压力增大以及客户服务水平受限等问题。为有效解决上述问题，支撑农投信用业务高质量发展，提升核心竞争力，亟需建设一套覆盖担保业务全生命周期、集业务运营、风险管理、财务管理、客户服务于一体的现代化担保业务管理系统。
系统建设目标
一是实现担保业务全流程（受理、尽调、评审、签约、保后、代偿、解保）线上化、标准化、自动化处理。
二是实现风险信息的集中管理、动态监控和智能预警。
三是打破数据孤岛，构建统一的数据基础和智能分析平台。
项目交付时间
2025年10月-2026年3月。
功能需求
	•	担保综合管理平台
	•	工作台
作为用户登录系统的主界面，所有用户登录系统后，都将提供该用户角色、岗位、权限所专有的首页内容，满足用户的操作便捷性要求，主要功能点包括以下内容：
通知消息：系统通知、文件查询下载等常用功能，并可以在公司内部人员和与各子机构之间通信和传送文件。
待办事项：提供当前登录用户的待办任务，直观准确的显示任务数和详情，支持快速办理。
已办事项：提供当前登录用户的已办任务。
预警信息：提供各类业务、各类场景的办理提醒、预警消息等，并支持快速处理。
业绩统计：系统登录人员可以查看本人的业绩信息以及业绩排名等，提供当前登录用户的关键业务信息，如客户数、业务数等，并支持快捷查看详情。
密码修改：提供当前登录人员修改密码功能。
	•	客户管理
系统将客户划分为企业客户和个人客户进行管理。
	•	（1）客户信息采集
登记时主要填写客户名称、证件类型和证件号码及联系人等关键信息，预留三方接口，支持线上化自动采集。
企业客户新增时，采集的信息包含客户类型、企业名称、社会信用代码、法定姓名、注册资本、营业开始日期、营业结束日期、通讯地址、邮政编码、联系人姓名、手机号码、客户经理、客户来源等。
个人客户新增时，采集的信息包含客户类别、证件类型、证件号码、客户姓名、手机号码、通讯地址、邮政编码、客户经理、客户来源等。
	•	（2）个人客户信息维护
管理个人客户的基本资料和经济财产情况，包括基础信息、职业信息、收支情况、个人履历、家庭成员、门店信息、资产负债、房屋土地、社会保险、商业保险、车辆资产、个人纳税及无形资产等信息。
基本信息：个人客户基本信息包含证件类型、证件号码、出生日期、客户性别、证件有效期开始日期、证件有限期结束日期、民族、婚姻状况、最高学历、居住状况、是否涉农、客户所属地区、户籍地址、通讯地址、现住址、邮政编码、手机号码、有无负债等。
职业信息：职业信息包含所在单位、单位性质、单位所属行业、单位电话、工作起始年份、职业、职务、单位所属区域、单位地址、单位规模、月收入、现工作单位时间等。
收支情况：收支情况包含月平均收入、年收入、月平均支出、年支出、主要收入来源、供养人数、备注等。
个人履历：个人履历信息包含起始时间、截止时间、学习/工作单位、学习/工作地点、职务、奖惩说明、学习/工作情况说明等。
家庭成员：家庭成员信息包含与客户关系、姓名、证件类型、证件号码、性别、联系电话、联系地址、工作单位、年收入、备注等。
门店信息：门店信息包含门店名称、门店地址、经营内容、月均收入、月均成本、年收入、年支出、毛利润、毛利率、备注等。
资产负债：
负债类型包含银行贷款、民间借贷、应付账款（预收账款）和其他负债。
银行贷款信息包含负债类型、贷款银行、贷款金额、放款日期、到期日期、还款方式、贷款类型、贷款余额、备注等。
民间借贷信息包含负债类型、贷款金额、贷款余额、对象、取得时间、还款时间、利率、与借款人关系、备注等。
应付账款（预收账款）信息包含负债类型、贷款金额、贷款余额、对象、性质、起始日期、到期日期、备注等。
其他负债信息包含负债类型、贷款金额、贷款余额、名称、起始日期、到期日期、备注等。
房屋土地：房地产信息包含是否成品房、结构类型、是否共同财产、取得方式、原始价值、币种、已还贷款期数、、按揭/抵押借款余额、是否个人住宅、房屋使用现状、建筑面积、地点、户型居室、房产类型、建成年代（整年份）、朝向、所在层、总楼层、特殊因素、土地证号、土地使用权类型、土地使用面积、土地评估单价、土地使用终止日期、土地使用剩余年限、丘地号、使用状态、建筑物设计用途、承租方、租赁期限、年租金、租金缴纳方式、预收租金时长、是否转租、转租方、转租期限、转租年租金、转租预收租金时长、说明等。
社会保险：社会保险信息包含纳税人、缴纳月份、缴纳日期、养老保险、医疗保险、失业保险、工伤保险、生育保险，其他说明。
商业保险：商业保险信息包含购买险种、保单金额、保费、保险起始日、保险到期日、投保人、投保人证件号码、投保人联系方式、被保险人、被保险人证件号码、被保险人联系方式、承保机构、销售机构、保险说明等。
车辆资产：车辆类信息包含车辆类型、品牌、型号、颜色、车牌号、识别码/车架号、发动机号、生产日期、行驶里程、取得方式、原始价值、发票号码、发票金额、是否营运车辆、使用状态、是否审验合格、审验到期日贷款余额、说明等。
个人纳税及无形资产：个人纳税及无形资产信息包含纳税人、纳税人证件号码、上年度纳税总额、是否逾期、金融资产说明、长期股权投资说明、专利权说明、商标权说明，其他无形资产说明。
	•	（3）企业客户信息维护
管理企业客户的基本资料和财务状况，包括基本信息、股东信息、银行账户、关联企业信息、上下游企业信息、竞争对手信息、借款人及主要关联人的债务情况、经营情况、企业资质、诉讼记录等信息。
基本信息：企业基本信息工商信息和法定代表人信息。
企业工商信息包含企业名称、统一社会信用代码、简称、邮政编码、电子邮箱、传真、贷款卡号、登记机关、营业开始日期、营业结束日期、成立日期、核准日期、注册资本金额、是否涉农、所有制类型、行业类别、注册地址、通讯地址、经营状态、公司性质、申请人与政府关系类别、主管部门、归属集团、所属地区、员工人数、上年度营业收入、企业规模、主营业务、经营范围等。
法定代表人信息包含法人姓名、法人证件类型、法人证件号码、法人性别、法人手机号、法人家庭住址、法人最高学历、法人工作履历、法人婚姻状况、法人配偶姓名、法人配偶证件类型、法人配偶证件号码等。
股东信息：股东信息支持企业股东和自然人股东。
企业股东信息包含自然人股东信息包含股东姓名、股东类型、证件类型、证件号码、手机号码、出资方式、认缴金额、认缴比例、实缴金额、实缴比例、实缴期限、出资日期、注册资本、实收资本、公司性质、成立日期、注册地、法定代表人、最终实际控制人、营业期限、股东情况简介、备注等。
自然人股东信息包含股东姓名、股东类型、证件类型、证件号码、手机号码、出资方式、认缴金额、认缴比例、实缴金额、实缴比例、实缴期限、出资日期、股东情况简介、备注等。
高管信息：高管信息包含高管人员名称、高管人员类别、证件类型、证件号码、性别、出生日期、婚姻状况、国籍、政治面貌、家庭住址、户口所在地、学历、学位、职称、现任职务、职务任期、在本单位工作年限、联系电话、传真、邮箱、股东或股东委派标识、科研成果、发明或重要奖惩、工作经历、备注等。    
银行账户：账户管理信息包含账户名称、账户用途、账号、开户行、启用状态、备注等。
关联企业信息：关联企业信息包含客户名称、关联企业主体名称、统一社会信用代码、关联关系、经济性质或类型、成立时间、注册地、注册资本、实收资本、营业期限、主营业务、法定代表人、最终实际控制人、联系人、联系电话、地址、备注。
上下游企业信息：上下游企业信息包含包含客户名称、上下游类型、上游企业名称/下游企业名称、统一社会信用代码、关联关系、联系人、联系电话、地址、贸易往来说明、备注。
竞争对手信息：竞争对手信息包含客户名称、对手企业名称、统一社会信用代码、关联关系、联系人、联系电话、地址、竞品说明、备注。
负债信息：
负债类型包含银行贷款、民间借贷、应付账款（预收账款）和其他负债。
银行贷款信息包含负债类型、贷款银行、贷款金额、放款日期、到期日期、还款方式、贷款类型、贷款余额、备注等。
民间借贷信息包含负债类型、贷款金额、贷款余额、对象、取得时间、还款时间、利率、与借款人关系、备注等。
应付账款（预收账款）信息包含负债类型、贷款金额、贷款余额、对象、性质、起始日期、到期日期、备注等。
其他负债信息包含负债类型、贷款金额、贷款余额、名称、起始日期、到期日期、备注等。
经营情况：经营情况信息包含客户名称、经营产品说明、经营情况说明、备注。
企业资质：企业资质说明信息包含资质名称、取得时间、生效日期、失效日期、资质说明、备注。
诉讼记录：诉讼记录包含客户名称、起诉人姓名、被起诉人、被起诉原因、被起诉金额、被起诉日期、是否已经判决、判决执行金额、判决执行日期、判决执行结果。
财务分析：
支持上传财务报表采集企业客户的财务信息，同期报表可分不同口径的报表上传，并人工确认某一口径的报表作为最终取值项。对已上传的多期报表进行对比分析，标明有差异的财务数据。
	•	（4）企业财务报表
管理企业客户的财务报表，支持根据既定模板进行导入，支持多期对比，支持财务指标计算。
	•	（5）客户评级
支持客户信用评级，包含评级申请和评级审批。信用评级，主要对客户资料进行分析评估，计算其信用等级，作为授信的基础。信用评测的数据来源，来自于客户提交和实调人员采集，以及大数据系统的填充。
可自定义配置评分参数，以及参数权重。针对不同行业，不同规模的企业，可配置不同的评价标准。数据完整填充后，系统会自动生成信评得分。
	•	（6）影像资料管理
支持灵活配置客户需要采集的资料类型，支持设置必填/非必填控制。支持上传客户相关要件资料，如证件扫描件、征信查询授权书等，影像资料支持：图片、Word、Excel、PDF格式文件支持在线查看，其它格式文件支持下载本地查看；要件支持必传控制；要件大小限制，默认10M；要件资料支持区分类别展示。分类标准支持灵活调整。
	•	（7）信息变更管理
客户资料涉及到新增和删除外，当客户情况发生了变化，还涉及到资料的更新。系统支持编辑客户资料，并会自动记录客户资料修改的痕迹。
	•	反担保物管理
	•	（1）信息管理
押品类别管理：管理押品类别，主要包含股票（股权）、房产/土地、应收账款、车辆等。

一级分类
二级分类
三级分类
A
金融质押品
1-现金及其等价物
存单、现汇、现金、保证金等
现金及其等价物


4-票据
银行本票、银行汇票、银行承兑汇票、人民银行发行的票据、我国中央政府投资的公用企业发行的票据、我国中央政府投资的公用企业承兑的汇票
票据


5-股票（权）/基金
上市公司股票（权）、非上市公司股票（权）、基金等
股票（权）




基金
B
商用房地产和居住用房地产
1-商用房地产
商业用房、工业用房、办公用房等
商用房地产


2-商用建设用地使用权
商业用地、工业用地、办公用地等
商用建设用地使用权


3-居住用房地产
居住用房等
居住用房地产


4-居住用建设用地使用权
居住用地等
居住用建设用地使用权


5-房屋类在建工程
商用房产类在建工程、居住用房产类在建工程
房屋类在建工程


6-其他房地产

其他房地产
C
应收账款
1-交易类应收账款
交易类应收账款等
交易类应收账款


4-其他收费权
城市基础设施收费权，通信、网络、有线电视收费权，学校及医院收费权，电源（发电）项目收费权，旅游景点收费权，提供其他公共服务产生的收费权
其他收费权


5-应收租金
应收租金等
应收租金


6-其他应收账款

其他应收账款
D
其他押品
1-流动资产
存货、仓单、提单、备用信用证、保函等
存货




仓单


3-机器设备
通用设备、专用设备、电气设备、电子产品及通信设备、仪表仪器、计量标准器具及量具、衡器等
通用设备




专用设备


4-交通运输设备
车辆、船舶、飞行设备，铁路等
车辆




船舶


5-设施类在建工程
设施类在建工程等
设施类在建工程


6-资源资产
采矿权，探矿权，林权等
林权


7-无形资产
可转让知识产权中的财产权等
无形资产


8-其他押品

其他押品
押品基础信息：客户名称、担保方式、押品类别、押品名称、类别名称、所有权人名称、所有权人证件类型、所有权人证件号码、抵质押率、说明等。
股票（股权）信息：股票（权）信息包含股权类型、股东名称、股权证书编号、占公司股权比例、、认缴出资金额、实际出资金额、出资时间、出资方式、质押股数、质押股份占持有股比例、说明等。
房产/土地信息：是否成品房、结构类型、是否共同财产、取得方式、原始价值、币种、已还贷款期数、、按揭/抵押借款余额、是否个人住宅、房屋使用现状、建筑面积、地点、户型居室、房产类型、建成年代（整年份）、朝向、所在层、总楼层、特殊因素、土地证号、土地使用权类型、土地使用面积、土地评估单价、土地使用终止日期、土地使用剩余年限、丘地号、使用状态、建筑物设计用途、承租方、租赁期限、年租金、租金缴纳方式、预收租金时长、是否转租、转租方、转租期限、转租年租金、转租预收租金时长、说明等。
应收账款信息：发票号码、开票日期、票面金额、发票币种、应收账款到期日、、应收账款生效日、支付方式、应收账款净值、付款条件、订单号码、主折扣率、主折扣天数、次折扣天数、次折扣率、说明等。
车辆信息：车辆类型、品牌、型号、颜色、车牌号、识别码/车架号、发动机号、生产日期、行驶里程、取得方式、原始价值、发票号码、发票金额、是否营运车辆、使用状态、是否审验合格、审验到期日贷款余额、说明等
登记押品权证信息：权证编号等
	•	（2）押品入库
客户经理提交押品入库申请，信息包含押品名称、保管类型、申请入库日期、存放地址、保管机构、保管人、经办人、负责人、入库说明。
经审批通过后，押品入库。
	•	（3）押品出库
担保业务完结后，支持押品出库，客户经理提交押品出库申请，信息包含押品名称、存放地址、保管机构、保管人、保管类型、出库日期、出库原因。
经审批通过后，押品出库。
	•	（4）押品查询
支持根据押品状态、所属客户等信息进行押品筛选查询。
	•	业务办理
	•	（1）项目立项
担保申请，由授权客户经理发起。
发起担保申请时，引入意向申请信息。此处可多选反担保方式，且不同反担保方式，需要录入对应的担保信息。如：选择房产抵押，则需要录入抵押物-房产的信息。
放款行名称为下拉选项，选项内容由系统管理员配置，是已经合作的放款方,放款行，可在审批中进行调整。
填写项目申请信息，包括产品、金额、期限、还款方式、反担保措施，融资银行或其他融资渠道等信息，提供担保材料清单包括营业执照复印件、开户许可证复印件、机构信用代码证复印件等相关公司资料，上传担保申请书等。
担保申请信息包含：
基本信息：客户名称、客户类型、发生类型、产品名称、担保金额、担保期限、期限类型、反担保方式、还款来源、项目背景、业务主办、业务B角；
融资信息：融资金额、融资期限、还款方式、贷款投向、明细投向、贷款用途、贷款用途说明、合作银行、银行客户经理、客户经理联系方式、项目来源、申请日期。
费用信息：费用名称、费用类型、费用计算基数、费用比例、费用收取方式。
	•	（2）项目尽调
尽职调查阶段，由项目调查A角进行撰写，B角进行审核确认。支持查看以及完善修改客户信息、分析财务数据、登记或导入反担保措施、录入费用标准、材料收集。
登记调查初审意见：责任比例、融资用途/还款来源等信息。
保前准入：根据业务不同，在风险平台设置风险准入模型，调用相关的保前风险模型进行准入拦截预警。
资料收集：完善业务办理的担保信息，上传业务相关的影像资料信息。
（3）担保审批
系统支持灵活配置审批流程，实现多人逐次进行审批。也可以配置并行审批流程。系统会自动记录每个节点的操作时间、操作人，以及审批意见。审批结论包含：审批通过、审批驳回、审批拒绝。当选择驳回或拒绝时，需要选择预先设定的格式化驳回/拒绝理由。
风控部针对担保业务给予风险审查意见，上传风险审查报告。
担保评审会，支持线上开会和线下开会两种模式。担保评审会针对符合规则条件的客户进行评审。
系统会获取审批事项， 录入会议记录。记录出席委员、列席委员各自的意见，以及评审结论。评审结论包含：通过、拒绝和复议。系统管理员可配置通过规则，如同意人数占比大于多少，才可通过。
担保审批确认，是对审批过程的总结。既包含各部门的意见，也包评审会、合作银行的各项意见，以及最终批复的额度和权限。用于存档备份，作为业务档案的一部分。
担保审批结束后，由担保公司向银行发送《担保确认函》, 此文件由系统自动生成，避免人工编辑引发错漏。支持导出PDF格式的文件。
	•	（4）合同生成
系统自动生成委托担保合同/银行合同/反担保合同模板，支持业务人员在线调整合同文本信息。
各类合同可通过系统自动生成，支持。
支持纸质签约方式。系统会推送消息给小程序客户端，由借款人和担保公司签署完成。合同模板可按需求自行配置。
支持人工填写信息签约方式、合同开始日、合同结束日、收款账号、收款账户户名、收款账户开户行、是否存在补充条款、补充条款、违约条款等。
	•	（5）合同审核
系统设置审批流程对合同进行审核确认,合同的签批，可配置律师用户,到此节点后，律师可审查合同。并修缮合同文本细节。
	•	（6）合同签订
合同审核通过后，下载合同文件，进行线下纸质签约方式签署。系统登记签约结果信息（结果信息与合同中信息保持一致）：签约日期、借款起止日期、担保金额(万元)、担保期限 (月)、贷款银行、放款方式、还款方式、收费方式等信息，上传已签约的合同文档（委托担保合同、借款合同、反担保合同）。
	•	（7）收费管理
登记费用到账信息，包含客户名称、合同编号、合同金额、合同期限、费用类型、计划收费日期、费用比例、收费金额、到账金额、到账日期、说明、收费账户、收费账号、开户银行。
如计划收取费用金额与实际到账金额不一致，需线下人工核实后，再进行确认。
系统以实际到账确认金额为准。
支持上传银行打款凭证。
	•	（8）放款审核
审核放款通知书，审批流程支持灵活设置，审批通过后，系统自动生成放款通知书意见表。
支持上传盖章后的放款通知书扫描件和反担保物权证资料。
此环节生成放款通知书，支持打印下载。支持多次放款。
	•	（9）放款登记
合作银行放款后，在系统中记录放款日期、放款账号、代偿利率等要素，并上传银行实际放款凭证，此节点结束后，业务进入保后管理节点。
同时，本环节支持导入借款还款计划信息。
	•	费用管理
	•	（1）费用设置
配置产品相关收费标准及收费环节如：担保费、服务费、咨询费等。
	•	（2）费用收取
系统自动生成费用计划。
根据资金流水自动适配/手动匹配费用收取情况。
	•	（3）保证金管理
存入保证金：管理客户存入保证金，单笔存入。
存出保证金：管理机构存出保证金，单笔存出。
保证金池管理：管理批量保证金，支持登记明细。
	•	保后管理
	•	（1）保后跟踪
保后检查设置：可自定义配置保后检查模型和检查类别。针对在保项目的客户进行保后检查，填写检查报告，报告分首次检查和常规检查。支持人工创建检查任务。
保后检查登记：
针对进入保后之后的客户进行保后检查，填写检查报告。
保后检查以客户为主体进行。
项目经理在收到保后检查任务后，线下完成检查，并登记检查报告。
支持上传保后检查资料
保后检查审批：可自定义保后检查审批流程，并支持是否走审批的设定
保后检查查询：支持查阅以往的检查报告，保后检查历史和检查结果。
	•	（2）还款登记
系统自动计算每期应还款金额，客户经理直接登记客户还款账号，还款人名称，还款开户行，实还金额、还款日期、还款本金、还款利息、还款总额、是否逾期。
	•	（3）担保结算
担保结算，对担保结束的客户进行结算清收。
解保信息包含客户名称、委保合同编号、合作银行、担保金额、担保期限、在保金额、解保类型、解保说明、解保日期、备注。
	•	代偿追偿管理
	•	代偿管理：管理代偿信息，支持代偿登记。
追偿管理：支持追偿申请、审批和到账确认。
	•	（5）担保解保/续保
担保到期后，可选择解保和续保。
针对项目完结的业务进行解保管理。 
对续保业务需要再次进行审批。
担保终止后，档案进行最终归档处理。
	•	（6）风险预警管理
风险管理：根据预警指标自动生成预警消息，提醒业务主办或风险管理人员或客户。预警包含：
编号
功能点
消息模板
1
保后首次预警
{客户经理名称}你好，{客户名称}的担保业务（{委托保证合同号}）需要进行保后首次检查，请及时处理
2
保后检查预警
{客户经理名称}你好，{客户名称}的担保业务（{委托保证合同号}）需要进行保后检查，请及时处理
3
担保到期检查
您好，您的项目：{委托保证合同号}即将到期，请尽快确认是否能顺利解保
4
缴款后通知放款
您好，您的客户{客户名称}{委托保证合同号}请尽快完成放款
5
放款审批通过后通知放款确认
您好，您的客户{客户名称}{委托保证合同号}请尽快完成放款登记
五级分类：根据设置的五级分类规则对履行中的业务进行自动五级分类。
预警管理：
支持预警规则和预警消息发送。
预警指标中的警戒值支持调整。
预警级别支持自定义。
预警消息支持系统消息推送。
预警信息每日夜间闲时处理，自动发送预警消息。
	•	（7）业务变更
支持客户和业务移交，支持单个或者批量进行移交。
	•	（8）资产管理
法律诉讼：在业务出现重大风险时，支持发起诉讼申请。
资产保全：当业务发生涉诉时，需要对涉诉业务进行资产保全，可根据项目诉讼情况新增资产进行保全管理。
	•	综合管理
	•	（1）资金机构管理
登记管理担保业务的资金放款机构，登记机构的保证金相关信息。管理合作的资金机构，登记信息包含资金机构名称、统一社会信用代码、资金机构简介、资金机构分类、联系人、联系方式、关联操作员，授信金额，授信有效期等。支持上传相关资料要件。
	•	（2）合作机构管理
管理业务中的合作方，如地方协会、地方政府、合作担保机构等。
	•	档案管理
	•	（1）档案归档
可将业务办理过程中所有附件及文档归集
（2）档案变更
归档后可临时补充、调整档案
	•	（3）借阅申请
针对需要借阅的档案，发起借阅申请
	•	（4）出借审批
对客户经理提交的借阅申请进行审批
	•	（5）档案归还
登记档案归还记录，包含归还人，归还时间等信息。
	•	财务管理
	•	（1）财务设置
科目管理：维护科目信息，支持科目新增、修改和删除。
记账规则配置：维护业务交易的记账规则。
辅助核算项配置：维护辅助核算项信息。
平台账户管理：维护公司银行账户，并与科目关联。
	•	（2）凭证管理
业务复核：涉及资金交易的场景支持进行业务复核，自动生成财务凭证。
凭证导出：支持财务凭证导出。
	•	统计报表
担保业务监控表：
根据相关领导层关注的内容，在我的工作台应以图形化、曲线图等直观图像展现公司担保业务总体情况、分类情况及发展情况等。包括但不限于企业类型分布、规模分布、行业分布、地区分布，在保余额等。
担保业务统计表：
满足担保业务管理个性化的报表，包括：统计报表、主债务合同报表、担保合同报表、反担保合同报表、保后跟踪统计报表、担保费用收取统计表等，提供多维度自定义条件分析统计等。
监控预警表：
支持图形化展示分析结果，支持可视化动态大屏分析。关注的数据异常有变化时预警提示在大屏上。
	•	系统设置
	•	（1）机构设置
新增部门：设置公司的组织机构，新增部门，支持添加多级部门，支持部门信息编辑修改以及删除操作。
新增员工：新增公司的员工账号，支持设置员工的角色、密码等信息；持设置员工的数据权限，数据权限包含：本人、本部门、本部门及其向下、指定部门、查看全部；持设置员工的使用权限，禁用/启用；持账户锁定后，进行账户解锁。
设置角色：配置角色权限，据岗位角色的职能，进行功能菜单的禁用/启用。
	•	（2）客户设置
客户类型设置：设置系统中的客户类型和相应的展示模块，配置客户信息中需要上传的要件类型和模板。
客户分类配置：设置客户的分类类别，可供客户分类为黑名单、优质客户、普通客户、潜在客户等，并可以自定义设置。
客户流程设置：设置客户相关的审批流程，包括客户评级、客户划分黑名单等功能是否启用审批流程以及具体流程内容的设置。
评级模型配置：设置客户评级模型，便于风控人员对客户，配置检查模型卡；支持配置风险检查指标项。按照客户财务报表模型在系统中配置对应的财务报表模板，业务系统中操作时可下载报表模板填写后进行上传。
	•	（3）产品设置
产品添加：支持管理业务产品，维护基本信息，包含产品名称、产品类型、产品定义、产品定位和参与主体等信息。
基础配置：管理金融方案信息，包含业务参数及产品额度，提供新增，修改、查询和删除等操作。
核算配置：支持选择核算参数，包含产品利息支付会计计算的配置及新增，修改、查询和删除等操作。
流程配置：支持担保业务流程、业务审批流程、合同审批流程、放款审批流程等流程配置，在流程工作平台中配置。
风控配置：支持核心风控理念、基本风控措施等手工录入或根据产品名称带出信息进行修改，支持配置风控方案的业务环节有：立项申请、尽职调查、放款申请、放款审批等业务环节。
费用配置：支持产品费用管理的配置及新增，修改、查询和删除等操作。
影像配置：支持产品所需附件勾选或根据产品名称带出产品附件进行新增，修改，上传、下载等业务。
模板配置：配置产品模板及新增，修改、查询和删除，上传、下载等业务操作。
	•	（4）保后设置
保后流程设置：管理保后相关的审批流程，包括保后检查、项目结算、项目终止等功能是否启用审批流程以及具体流程内容的设置。审批流程支持自定义。
保后跟踪模型配置：配置保后跟踪模型，包括保后跟踪类型，跟踪内容以及生成相应的保后跟踪报告等。
支持设置首次跟踪模型、常规跟踪模型的适配属性：客户类型、产品种类、还款方式、担保方式、金额上下限、期限上下限等；
支持设置首次跟踪模型、常规跟踪模型的检查项；
支持设置首次跟踪模型、常规跟踪模型的要件资料、文档资料。
	•	（5）基础设置
文档配置：配置要件资料类型，登记文档类型、文档名称、文档说明启用标志，设置文档的禁用启用；配置后，支持在产品配置、客户配置等功能中引用。
模板配置：配置系统中所使用的文档模板，支持模板的禁用/启用，支持标签配置，支持合同模板中要自动取值的文本字段加入标签。
风险拦截项配置：配置系统中的风险拦截项，拦截项方案支持SQL以及java类两种，SQL需要直接使用SQL语句查询，java类需要配置java类方法来做。
客户与业务清理：清理系统中的业务和客户信息。支持对业务系统中的脏数据进行物理清理；支持一键清理以及按部门清理数据。
审批角色/用户配置：配置系统中所有审批的审批岗位和对应的审批人员，支持新增、修改和删除操作。
系统logo设置：配置系统中登录界面和首页展示的logo，满足登录页面个性化设置的需求。
保后预警：配置保后检查预警、客户还款预警、催收预警提醒。支持配置最大预警次数；支持配置预警类型、预警接收人、预警接收角色等基础信息；支持预警发送类型：短信、内部消息。
	•	移动端管理
面向机构内部员工，以页面嵌入方式，集成至办公平台入口，如钉钉、企微等。主要实现功能包含：客户新增、客户信息查看、资料上传、业务查询、线上审批、保后检查、业务预警、业务统计、消息通知。
	•	（1）客户管理
管理维护客户的各项信息，根据PC端的各类信息保持一致。支持查询每个客户的业务申请必输，在履行情况，结清情况统计。
	•	（2）资料上传
管理维护客户以及业务的相关影像资料信息，已客户为主题，支持查看客户公共的资料以及每笔业务关联的资料信息。支持上传图片、PDF等格式的资料。
	•	（3）业务查询
支持按照客户名称、证件号码、合同编号、业务状态进行合同精确查询。
	•	（4）线上审批
在消息通知通达审批人进行审批。业务管理过程中支持业务审批、合同审批、放款审批、保后检查审批等众多审批与PC端的待办审批保持一致。点击审批支持在线登记审批意见以及审批意见描述，支持上传相关审核资料信息。
	•	（5）保后检查
系统后台会将带跟进的保后检查任务推送至移动端，支持指定推送或者有权限的人认领机制。内部人员能看到推送自己的待办检查任务，支持查询待检查、检查审批中、会检查、忽略等多种任务。人工编著检查报告，上传检查报告文档。
	•	（6）业务预警
支持客户查看业务贷款发放提醒、贷款到期预警提醒，贷款逾期提醒、还款到期预警提醒，还款逾期预警提醒。
	•	（7）业务统计
按照岗位角色进行分类统计，统计担保客户数、担保金额、担保笔数、担保余额，贷款笔数。呈现方式主要是图表形式展示。
	•	（8）消息通知
企微/钉钉通知：业务在受理成功，签约提醒、审批通过、放款成功、业务拒绝等多个节点预设通达消息，在业务管理过程中，业务触发后，实时将通知消息通达给借款客户。
	•	历史数据迁移
	•	（1）历史数据迁移分析
分析系统以及综合业务系统的数据结构，业务状态，影像资料，业务实现的逻辑，将两个系统功能以及数据对应关系匹配起来。梳理历史系统以及综合业务系统的数据匹配度。
	•	（2）数据导入
业务数据建议采用结构化的表格进行批量初始化导入。
	•	（3）数据校验
批量验证，设置重点的数量、金额、状态等关键数据校验指标进行抽查、逐笔、全量等多维度进行数据校验。
	•	（4）数据投产
历史存量数据投产正常运行。
系统集成需求
	•	集团内部系统集成
	•	（1）集团统一身份认证系统
实现统一业务系统入口和统一待办管理。
	•	（2）合同管理
对接集团合同管理平台，实现合同生成、审核审批统一管理。
	•	（4）集团资金管理（司库）系统
对接集团资金管理（司库）系统，实现费用、保证金等业务场景自动处理。
	•	（5）集团企微
面向机构内部员工，以页面嵌入方式实现内部移动化办公。
	•	（6）担保公司钉钉
面向机构内部员工，以页面嵌入方式实现内部移动化办公。
	•	外部系统集成
（1）客户三方数据
对接三方数据，满足客户信息自动填充、客户评级、业务初审等风控需求。(通过普汇通数据产品统一对接)
	•	（2）短信通道
业务消息提醒。
技术开发需求
	•	平台技术要求
（1）开发平台应采用微服务架构；支持分布式部署以保证系统给的高可用性；支持平行扩展以支持未来的业务增长。
（2）开发平台应具有可扩展性，具有完整、先进、灵活等特点，允许用户在软件的基础上方便地进行二次开发，实施后方便地建立新的需求应用；
（3）开发平台应提供详细的开发文档，提供除平台外应用系统所有开发源代码、设计和开发文档、各类帮助文档和用户手册，并提供系统维护及开发培训；系统代码应当符合良好的编码规范，具有较好的可读性和可维护性；
（4）提供完善的权限管理方案，可以根据不同的角色授予相应的权限，操作不同用例和使用具体功能。
（5）具备完善的容错、异常处理机制和故障隔离机制。网络、数据库等中断后，要具备自行恢复的能力；
（6）系统日志分级清晰，分类清楚，时间点明确，内容连贯、含义清楚，方便查找错误和问题。能够提供详细有效的系统运行、用户使用等日志，便于对故障、事件和错误等进行分析和定位，方便事件处理和解决。有自动的日终数据备份或日志清理等功能。
（7）开发平台及其应用系统及部署方案应能通过第三方的安全渗透测试，能有效的保护业务和客户数据不被窃取、不被篡改、不会丢失。
	•	性能要求
（1）前台业务用例响应时间小于 2 秒；
（2）统计查询用例响应时间小于 5 秒；
（3）系统登录时间小于 2 秒，通过率达 100%；
（4）优化模型设计，减少冗余数据量的加载和检索，以及表间关联检索。
（5）有效利用分布式缓存功能，对于经常访问的数据，可将数据缓存于内存池中，减少 IO读写。
（6）优化报表设计，减少报表生成所需要的系统资源。
（7）充分利用报表系统的缓存功能，把报表生成任务安排到非高峰时段。
（8）充分利用报表系统的对查询的缓存功能，减少对数据源的实时访问。
	•	系统安全与稳定性需求
（1）数据交换应采用信息非对称加密传输，保证数据传输过程中的完整性和保密性，提供严格的加密机制和认证机制，严禁非法用户对数据的修改。
（2）实现系统高可用，除制度规定的系统正常检修与维护外，系统需保持7X24小时运行，包括数据库服务，同时实现系统数据的定时备份，并确保数据恢复的可用性与及时性；；
（3）确保系统的安全管理具有高可靠性，并具有可审计、可监控性；
（4）实现安全系统管理的多层次、最小权限以及访问控制机制，防止黑客对门户等应用的恶意攻击。
	•	系统扩展性需求
系统应充分满足当前用户的工作需求，并考虑今后业务的扩展及数据增大后的各种应用，实现系统功能的高效运转。
（1）软件版本易于升级，任何一个业务功能模块的维护和更新以及新模块的追加都不应影响其它模块原有功能，且不影响系统的性能与运行。
（2）应能提供支持用户自定义的工作流引擎，用户能根据业务需求自定义业务流程。
（3）系统能提供丰富的数据接口，实现不同格式数据(Excel、MDF、LDF)的导入与导出，以及有关统计图表的Word、PDF、WPS等格式的输出。
（4）系统模块化设计，支持参数化配置，支持组件及组件的动态加载，能方便地进行功能组和客户化功能设置，包括应用界面设置、功能的增减、数据库表和表字段的增加等。
系统部署需求
在项目实施及免费运维阶段各种环境下（包含但不限于：开发环境、测试环境、生产环境、文档管理环境）服务器的搭建（包括但不限于：操作系统、中间件、数据库、开发工具）。
类别
技术参数
系统技术平台
本系统需基于Java开发，开发框架为Spring Boot
应用服务器技术要求
Redhat 7+、centos7+或者Windows 2008 64位操作系统及以上版本。

能够同时支持水平和垂直负载的部署方式
系统开发工具
要求使用JDK1.8以上版本，IntelliJ IDEA或Eclipse等。
数据库
MySQL5.7及以上版本；Redis3.0.5及以上版本。
客户端要求
系统需支持用户通过主流浏览器(如google chrome,Microsoft Edge)访问。
配套资源
为满足系统运行、测试、数据存储、文件备份等功能，需提供系统部署所需资源如下图。

测试环境服务器部署方案
编号
服务器
描述
数量
配置要求
1
业务服务器
部署主业务应用微服务
1（至少）
8核32G内存，200G及以上硬盘
2
数据库服务器
部署redis,MYSQL5.7数据库的服务器
1（至少）
8核16G内存，200G及以上硬盘





生产环境服务器部署方案
编号
服务器
描述
数量
配置要求
1
应用服务器
基础微服务
1（至少）
8核 8G内存，200G及以上硬盘
2
应用服务器
移动端入口微服务
1（至少）
8核16G内存，200G及以上硬盘
3
应用服务器
前端业务系统微服务
1（至少）
8核32G内存，200G及以上硬盘
4
应用服务器
批量程序及调度任务
1（至少）
8核16G内存，200G及以上硬盘
5
应用服务器
主业务应用微服务
1（至少）
8核32G内存，200G及以上硬盘
6
数据库服务器
部署redis,MYSQL5.7数据库的服务器
1（至少）
8核32G内存，500G及以上硬盘
7
文件服务器
NFS文件服务器
1（至少）
8核 8G内存，1TB及以上硬盘（具体看实际情况业务）

测试要求
依据《需求规格说明书》完成单元、集成、系统、用户验收四级测试，测试类型至少覆盖功能、性能、安全、兼容性四项；所有用例100%执行，系统测试缺陷密度≤0.2个/功能点，性能指标须达到并发≥500TPS、响应≤3s，安全扫描0高危漏洞。
测试完成后，须出具加盖公章的《测试报告》并通过业主方评审后方可上线。
项目培训要求
1.系统厂商应负责系统采购方业务、技术人员的培训，应免费提供系统的软、硬件配置、安装、调试、操作、维护、故障排除等方面的培训，使系统采购方相关负责人员熟悉系统的使用与维护。使用到的第三方中间件及软件均需要提供足够深度的高级培训，保证完成技术移交，并提供全套的培训教材和培训课程计划表。
2.系统厂商应负责系统采购方完成业务操作人员培训工作。
数据备份要求
具备自动的日终数据备份。
系统日志分级清晰，分类清楚，时间点明确，内容连贯、含义清楚，方便查找错误和问题。能够提供详细有效的系统运行、用户使用等日志，便于对故障、事件和错误等进行分析和定位，方便事件处理和解决。
有日志清理等功能。
灾难恢复计划
具备完善的容错、异常处理机制和故障隔离机制。网络、数据库等中断后，要具备自行恢复的能力。
遇到灾难性宕机后，系统恢复服务的时间要求在4小时以内。
宕机重处理后，数据恢复率要求达到100%。
技术支持要求
维保服务要求
提供7*24的维保服务，可提供现场解决故障的服务。服务标准为：接到故障电话后，60分钟内响应，紧急情况下能赶到现场，2小时内恢复系统。
系统投产后提供一年免费的保修（维护期从系统投产验收合格之日开始计算，写入合同条款），保修内容包括电话咨询服务、现场处理故障服务、修复BUG、需求改进、下载补丁以及小版本免费升级。保修期间，要求每季度做系统巡查，提供健康报告，定期做性能调优及技术交流。如投标人另有承诺增加质保期的，按其承诺质保期执行。
系统升级服务要求
系统版本每次升级时要免费提供详细升级计划和方案，尤其是针对客户化功能的升级方案，并提供人员培训。

